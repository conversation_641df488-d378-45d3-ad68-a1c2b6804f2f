package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.payne.server.banknote.deserializer.SpecialMarkDeserializer;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 送评单明细实体类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Entity
@Table(name = "PJ_O_SENDFORM_ITEM")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PJ_O_SENDFORM_ITEM")
public class PjOSendformItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 送评单号
     */
    @Column(name = "SENDNUM", columnDefinition = "VARCHAR2(50)")
    @TableField("SENDNUM")
    private String sendnum;
    
    /**
     * 序号
     */
    @Column(name = "SEQNO", columnDefinition = "NUMBER(10)")
    @TableField("SEQNO")
    private Integer seqno;
    
    /**
     * 钱币类型
     */
    @Column(name = "COIN_TYPE", columnDefinition = "VARCHAR2(50)")
    @TableField("COIN_TYPE")
    private String coinType;
    
    /**
     * 年份
     */
    @Column(name = "COIN_YEAR", columnDefinition = "VARCHAR2(20)")
    @TableField("COIN_YEAR")
    private String year;
    
    /**
     * 面值
     */
    @Column(name = "FACE_VALUE", columnDefinition = "VARCHAR2(50)")
    @TableField("FACE_VALUE")
    private String faceValue;
    
    /**
     * 版别
     */
    @Column(name = "COIN_VERSION", columnDefinition = "VARCHAR2(100)")
    @TableField("COIN_VERSION")
    private String version;
    
    /**
     * 数量
     */
    @Column(name = "QUANTITY", columnDefinition = "NUMBER(10)")
    @TableField("QUANTITY")
    private Integer quantity;
    
    /**
     * 评级费
     */
    @Column(name = "GRADE_FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("GRADE_FEE")
    private BigDecimal gradeFee;
    
    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = "VARCHAR2(500)")
    @TableField("REMARK")
    private String remark;
    
    /**
     * 送评条码
     */
    @Column(name = "DIY_CODE", columnDefinition = "VARCHAR2(50)")
    @TableField("DIY_CODE")
    private String diyCode;
    
    /**
     * 钱币编号（冠号）
     */
    @Column(name = "SERIAL_NUMBER", columnDefinition = "VARCHAR2(50)")
    @TableField("SERIAL_NUMBER")
    private String serialNumber;
    
    /**
     * 钱币名称1
     */
    @Column(name = "COIN_NAME1", columnDefinition = "VARCHAR2(200)")
    @TableField("COIN_NAME1")
    private String coinName1;
    
    /**
     * 钱币名称2
     */
    @Column(name = "COIN_NAME2", columnDefinition = "VARCHAR2(200)")
    @TableField("COIN_NAME2")
    private String coinName2;
    
    /**
     * 钱币名称3
     */
    @Column(name = "COIN_NAME3", columnDefinition = "VARCHAR2(200)")
    @TableField("COIN_NAME3")
    private String coinName3;
    
    /**
     * 面值
     */
    @Column(name = "FACE_VAL", columnDefinition = "VARCHAR2(50)")
    @TableField("FACE_VAL")
    private String faceVal;
    
    /**
     * 所属名称
     */
    @Column(name = "BELONG_NAME", columnDefinition = "VARCHAR2(200)")
    @TableField("BELONG_NAME")
    private String belongName;
    
    /**
     * 重量名称
     */
    @Column(name = "WEIGHT_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("WEIGHT_NAME")
    private String weightName;
    
    /**
     * 材质
     */
    @Column(name = "MATERIAL", columnDefinition = "VARCHAR2(50)")
    @TableField("MATERIAL")
    private String material;
    
    /**
     * 盒子类型
     */
    @Column(name = "BOX_TYPE", columnDefinition = "VARCHAR2(100)")
    @TableField("BOX_TYPE")
    private String boxType;
    
    /**
     * 标准价
     */
    @Column(name = "STANDARD_PRICE", columnDefinition = "NUMBER(10,2)")
    @TableField("STANDARD_PRICE")
    private BigDecimal standardPrice;
    
    /**
     * 国际价
     */
    @Column(name = "INTERNATIONAL_PRICE", columnDefinition = "NUMBER(10,2)")
    @TableField("INTERNATIONAL_PRICE")
    private BigDecimal internationalPrice;
    
    /**
     * 费用
     */
    @Column(name = "FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("FEE")
    private BigDecimal fee;
    
    /**
     * 折扣
     */
    @Column(name = "DISCOUNT", columnDefinition = "NUMBER(5,2)")
    @TableField("DISCOUNT")
    private BigDecimal discount;
    
    /**
     * 尺寸
     */
    @Column(name = "COIN_SIZE", columnDefinition = "VARCHAR2(100)")
    @TableField("COIN_SIZE")
    private String coinSize;

    /**
     * 重量
     */
    @Column(name = "COIN_WEIGHT", columnDefinition = "NUMBER(10,3)")
    @TableField("COIN_WEIGHT")
    private BigDecimal coinWeight;
    
    /**
     * 年代
     */
    @Column(name = "YEAR_INFO", columnDefinition = "VARCHAR2(50)")
    @TableField("YEAR_INFO")
    private String yearInfo;
    
    /**
     * 地区
     */
    @Column(name = "REGION", columnDefinition = "VARCHAR2(100)")
    @TableField("REGION")
    private String region;
    
    /**
     * 税种
     */
    @Column(name = "TAX_TYPE", columnDefinition = "VARCHAR2(50)")
    @TableField("TAX_TYPE")
    private String taxType;
    
    /**
     * 盒子费
     */
    @Column(name = "BOX_FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("BOX_FEE")
    private BigDecimal boxFee;

    /**
     * 加急费
     */
    @Column(name = "URGENT_FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("URGENT_FEE")
    private BigDecimal urgentFee;
    
    /**
     * 目录
     */
    @Column(name = "CATALOG", columnDefinition = "VARCHAR2(200)")
    @TableField("CATALOG")
    private String catalog;
    /**
     * 等级
     */
    @Column(name = "RANK", columnDefinition = "VARCHAR2(200)")
    @TableField("RANK")
    private String rank;

    /**
     * 银行名称
     */
    @Column(name = "BANK_NAME", columnDefinition = "VARCHAR2(200)")
    @TableField("BANK_NAME")
    private String bankName;
    
    /**
     * 特殊标记
     */
    @Column(name = "SPECIAL_MARK", columnDefinition = "VARCHAR2(500)")
    @TableField("SPECIAL_MARK")
    @JsonDeserialize(using = SpecialMarkDeserializer.class)
    private String specialMark;
    
    /**
     * 对内备注
     */
    @Column(name = "INTERNAL_NOTE", columnDefinition = "VARCHAR2(1000)")
    @TableField("INTERNAL_NOTE")
    private String internalNote;
    
    /**
     * 对外备注
     */
    @Column(name = "EXTERNAL_NOTE", columnDefinition = "VARCHAR2(1000)")
    @TableField("EXTERNAL_NOTE")
    private String externalNote;
    /**
     * 特殊标签
     */
    @Column(name = "SPECIAL_LABEL", columnDefinition = "VARCHAR2(1000)")
    @TableField("SPECIAL_LABEL")
    private String specialLabel;

    /**
     * 真伪鉴定
     */
    @Column(name = "AUTHENTICITY", columnDefinition = "VARCHAR2(50)")
    @TableField("AUTHENTICITY")
    private String authenticity;
    
    /**
     * 评级品相分值
     */
    @Column(name = "GRADE_SCORE_VALUE", columnDefinition = "VARCHAR2(50)")
    @TableField("GRADE_SCORE_VALUE")
    private String gradeScoreValue;
    /**
     * 评级打分/品相
     */
    @Column(name = "GRADE_SCORE", columnDefinition = "VARCHAR2(50)")
    @TableField("GRADE_SCORE")
    private String gradeScore;
    /**
     * 缺陷修补/评分备注
     */
    @Column(name = "SCORE_REMARKS", columnDefinition = "VARCHAR2(50)")
    @TableField("SCORE_REMARKS")
    private String scoreRemarks;
    
    /**
     * 验货标注
     */
    @Column(name = "INSPECTION_NOTE", columnDefinition = "VARCHAR2(500)")
    @TableField("INSPECTION_NOTE")
    private String inspectionNote;
    
    /**
     * 钱币正面图片
     */
    @Column(name = "FRONT_IMAGE", columnDefinition = "VARCHAR2(1000)")
    @TableField("FRONT_IMAGE")
    private String frontImage;

    /**
     * 钱币反面图片
     */
    @Column(name = "BACK_IMAGE", columnDefinition = "VARCHAR2(1000)")
    @TableField("BACK_IMAGE")
    private String backImage;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
} 